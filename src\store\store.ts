import { configureStore } from '@reduxjs/toolkit';
import counterReducer from '@/slices/counterSlice';
import { userApi } from '@/services/userApi';

export function makeStore() {
  const store = configureStore({
    reducer: {
      counter: counterReducer,
      [userApi.reducerPath]: userApi.reducer, // Add API reducer here
    },
    middleware: getDefaultMiddleware =>
      getDefaultMiddleware().concat(userApi.middleware), // Add API middleware here
  });
  return store;
}

// Type definitions
export type AppStore = ReturnType<typeof makeStore>;
export type RootState = ReturnType<AppStore['getState']>;
export type AppDispatch = AppStore['dispatch'];

'use client';

import ExploreRecommendation from '@/screen/ExploreRecommendation';
import { useLandingData } from '@/hooks/useLandingData';

export default function ExplorePage() {
  const { filter, chat, recommendations } = useLandingData();

  return (
    <div>
      <ExploreRecommendation
        loadingComponents={{
          filter: filter.isLoading,
          chat: chat.isLoading,
          recommendations: recommendations.isLoading,
        }}
      />
    </div>
  );
}

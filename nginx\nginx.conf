user nginx;
worker_processes auto;

events {
    worker_connections 1024;
}

http {
    include mime.types;
    default_type application/json;
    sendfile on;
    keepalive_requests 200;
    keepalive_timeout 65;
    client_max_body_size 10m;
    client_body_buffer_size 2m;

    log_format main '$remote_addr - $remote_user [$time_local] '
                    '"$request" $status $body_bytes_sent '
                    '"$http_referer" "$http_user_agent" "$gzip_ratio"';
    access_log /var/log/nginx/nxvoy-access.log;
    error_log /var/log/nginx/nxvoy-error.log;

    server {
        listen 80;
        server_name preprod.nxvoytrips.ai;

        location / {
            proxy_set_header X-Forwarded-For $remote_addr;
            proxy_set_header Host $http_host;
            proxy_pass http://localhost:3000;
        }
    }
}
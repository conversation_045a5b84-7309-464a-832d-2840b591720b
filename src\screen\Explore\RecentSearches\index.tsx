'use client';

import { Image } from '@heroui/react';

import CountriesGlobe from './CountriesGlobe';
import RecentSearchesCard from './RecentSearchesCard';

const RecentSearches = () => {
  return (
    <div className="my-5">
      <div>
        <p className="text-4xl font-semibold">Your recent searches</p>
        <div>
          <div className="flex flex-row items-center gap-4 my-4">
            <Image
              alt="recent search"
              className="min-w-full object-cover shadow-none"
              radius="sm"
              src="https://storage.googleapis.com/nxvoytrips-img/nxvoy-travel-agent-holidayPlanner/81ab010bbe76b5a0f44cb9eeaeadb5f68d795b83.jpg"
              width={100}
              height={100}
            />

            <div className="">
              <p className="text-base font-bold text-start text-[#25233A]">
                Istanbul, Turkey
              </p>
              <p className="text-[#868383] text-sm mt-1 leading-6 items-end">
                325 places
              </p>
            </div>
          </div>
        </div>
      </div>
      <CountriesGlobe />
      <div className="mt-4">
        <RecentSearchesCard />
      </div>
    </div>
  );
};

export default RecentSearches;

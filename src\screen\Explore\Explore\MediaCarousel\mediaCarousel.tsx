import { FiChevronLeft, FiChevronRight } from 'react-icons/fi';

import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from '@/components/ui/carousel';

import MediaCardComp from './card';

const data = [
  {
    img: 'https://storage.googleapis.com/nxvoytrips-img/nxvoy-travel-agent-holidayPlanner/92a9023cbe8c9a730a9130834387370f2b4af800.jpg',
    heading: 'Luxurious Goa',
    description: 'Starting at $1199 per person',
  },
  {
    img: 'https://storage.googleapis.com/nxvoytrips-img/nxvoy-travel-agent-holidayPlanner/81ab010bbe76b5a0f44cb9eeaeadb5f68d795b83.jpg',
    heading: 'Exquisite Kerala',
    description: 'Starting at $1099 per person',
  },
  {
    img: 'https://storage.googleapis.com/nxvoytrips-img/nxvoy-travel-agent-holidayPlanner/92a9023cbe8c9a730a9130834387370f2b4af800.jpg',
    heading: 'Luxurious Goa',
    description: 'Starting at $1199 per person',
  },
  {
    img: 'https://storage.googleapis.com/nxvoytrips-img/nxvoy-travel-agent-holidayPlanner/81ab010bbe76b5a0f44cb9eeaeadb5f68d795b83.jpg',
    heading: 'Exquisite Kerala',
    description: 'Starting at $1099 per person',
  },
  {
    img: 'https://storage.googleapis.com/nxvoytrips-img/nxvoy-travel-agent-holidayPlanner/92a9023cbe8c9a730a9130834387370f2b4af800.jpg',
    heading: 'Luxurious Goa',
    description: 'Starting at $1199 per person',
  },

  {
    img: 'https://storage.googleapis.com/nxvoytrips-img/nxvoy-travel-agent-holidayPlanner/81ab010bbe76b5a0f44cb9eeaeadb5f68d795b83.jpg',
    heading: 'Luxurious Goa',
    description: 'Starting at $1199 per person',
  },
  {
    img: 'https://storage.googleapis.com/nxvoytrips-img/nxvoy-travel-agent-holidayPlanner/81ab010bbe76b5a0f44cb9eeaeadb5f68d795b83.jpg',
    heading: 'Luxurious Goa',
    description: 'Starting at $1199 per person',
  },

  {
    img: 'https://storage.googleapis.com/nxvoytrips-img/nxvoy-travel-agent-holidayPlanner/92a9023cbe8c9a730a9130834387370f2b4af800.jpg',
    heading: 'Luxurious Goa',
    description: 'Starting at $1199 per person',
  },

  {
    img: 'https://storage.googleapis.com/nxvoytrips-img/nxvoy-travel-agent-holidayPlanner/92a9023cbe8c9a730a9130834387370f2b4af800.jpg',
    heading: 'Luxurious Goa',
    description: 'Starting at $1199 per person',
  },

  {
    img: 'https://storage.googleapis.com/nxvoytrips-img/nxvoy-travel-agent-holidayPlanner/92a9023cbe8c9a730a9130834387370f2b4af800.jpg',
    heading: 'Luxurious Goa',
    description: 'Starting at $1199 per person',
  },
];

const RecentProduct = () => {
  return (
    <div className="px-10">
      <Carousel className="w-full mb-0">
        <CarouselContent className="overflow-visible">
          {data.map(item => (
            <CarouselItem
              key={`media-item-${item.heading}`}
              className="md:basis-1/2 lg:basis-1/3 xl:basis-1/4 py-6"
            >
              <MediaCardComp item={item} />
            </CarouselItem>
          ))}
        </CarouselContent>
        {/* Custom Previous Button with Icon */}
        <CarouselPrevious
          className="absolute -left-12 top-1/2 -translate-y-1/2 rounded-full cursor-pointer"
          variant="ghost"
        >
          <FiChevronLeft className="w-6 h-6" />
        </CarouselPrevious>

        {/* Custom Next Button with Icon */}
        <CarouselNext
          className="absolute -right-12 top-1/2 -translate-y-1/2 text-subtitle p-2 rounded-full"
          variant="ghost"
        >
          <FiChevronRight className="w-6 h-6" />
        </CarouselNext>
      </Carousel>
    </div>
  );
};

export default RecentProduct;

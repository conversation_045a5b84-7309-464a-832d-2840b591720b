import { NextResponse } from 'next/server';

export function middleware() {
  // Example: redirect if not authenticated
  // const token = request.cookies.get('token')?.value;

  // if (!token && request.nextUrl.pathname !== '/login') {
  //   return NextResponse.redirect(new URL('/login', request.url));
  // }

  return NextResponse.next();
}

export const config = {
  matcher: [
    '/((?!_next|.*\\.(?:css|js|png|jpg|jpeg|svg|gif|ico|woff2?|ttf|json|map|txt|xml|webmanifest)).*)',
    '/api/:path*',
    '/trpc/:path*',
  ],
};

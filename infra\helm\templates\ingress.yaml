{{- if eq .Values.ingress.enabled "true" }} 
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: {{ .Values.app.name }}
  annotations:
    {{- toYaml .Values.ingress.annotations | nindent 4 }}
spec:
  # ingressClassName: {{ .Values.ingress.ingressClassName }}
  rules:
  {{- range .Values.ingress.hosts }}
    - host: {{ .host }}
      http:
        paths:
          {{- range .paths }}
          - path: {{ .path }}     
            pathType: Prefix
            backend:
              service:
                name: {{ $.Values.service.name }}
                port:
                  name: http
          {{- end}}              
  {{- end}}
{{- end }}    
'use client';

import { useState } from 'react';
import { SortIcon } from '@/components/icons';
import RecommendationCard from './card';
import { recommendations as recommendationsData } from '@/types/recommendationsData';
import RecommendationFilter from '@/components/globalComponents/Filter/RecommendationFilter';

const Recommendation = () => {
  const [filterOpen, setFilterOpen] = useState(false);

  return (
    <div className="relative">
      {/* Header */}
      <div className="flex flex-row items-center justify-between flex-shrink-0 md:sticky md:top-0 md:z-50 mb-2 bg-containerbg">
        <div>
          <p className="text-lg font-bold">Recommendation</p>
        </div>
        <div
          className="flex flex-row items-center gap-2 cursor-pointer"
          onClick={() => setFilterOpen(true)}
          onKeyDown={e => {
            if (e.key === 'Enter' || e.key === ' ') {
              e.preventDefault();
              setFilterOpen(true);
            }
          }}
          role="button"
          tabIndex={0}
          aria-label="Open filter options"
        >
          <SortIcon isAnimation={false} className="text-default-Secondary" />
          <p className="text-sm font-medium text-default-Secondary">Filter</p>
        </div>
      </div>

      {/* Cards */}
      {recommendationsData.map((item, index) => (
        <RecommendationCard
          key={item.title || `recommendation-${index}`}
          recommendations={item}
        />
      ))}

      {/* Filter Modal */}
      <RecommendationFilter
        open={filterOpen}
        onClose={() => setFilterOpen(false)}
        onApply={() => {
          // Handle filter application logic here
          setFilterOpen(false);
        }}
      />
    </div>
  );
};

export default Recommendation;

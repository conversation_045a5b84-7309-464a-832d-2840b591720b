import {
  <PERSON><PERSON>,
  <PERSON>dal<PERSON>ontent,
  ModalBody,
  Button,
  useDisclosure,
} from '@heroui/react';
import { useEffect, useState } from 'react';
import { FaApple } from 'react-icons/fa';
import { FcGoogle } from 'react-icons/fc';

export default function LoginModal({ open }: { open: boolean }) {
  const { isOpen, onOpen, onClose, onOpenChange } = useDisclosure();

  useEffect(() => {
    if (open) {
      onOpen();
    } else {
      onClose();
    }
  }, [open, onOpen, onClose]);

  const [emailOrPhone, setEmailOrPhone] = useState('');
  const [password, setPassword] = useState('');

  const handleSignIn = () => {
    localStorage.setItem('userEmailOrPhone', emailOrPhone);
    localStorage.setItem('userPassword', password);
    window.location.reload();
  };

  return (
    <Modal isOpen={isOpen} onOpenChange={onOpenChange} size="5xl">
      <ModalContent>
        <ModalBody>
          <div className="grid grid-cols-2 max-md:grid-cols-1">
            {/* Left section */}
            <div className="w-full p-10 space-y-5 max-md:p-4">
              <h2 className="text-3xl font-bold text-gradient1">
                Welcome Back, Traveller!
              </h2>
              <p className="text-[#080236]">
                Sign in to pick up right where you left off. We hope you're
                enjoying planning your trips with Shasa!
              </p>
              <p className="text-sm text-[#080236]">
                Still travelling without a NxVoy account?{' '}
                <p className="text-blue-600 font-semibold">Sign Up Here!</p>
              </p>

              <div>
                <label className="block font-semibold mb-1 text-[#1E1E76]">
                  User Name / Phone Number
                </label>
                <input
                  type="text"
                  value={emailOrPhone}
                  onChange={e => setEmailOrPhone(e.target.value)}
                  placeholder="Enter Your email Id / Phone Number"
                  className="w-full px-4 py-2 rounded-full border border-gray-300 focus:ring-2 focus:ring-purple-300 outline-none"
                />
              </div>
              <div>
                <div className="flex items-center justify-between mb-1">
                  <label className="block font-semibold mb-0 text-[#1E1E76]">
                    Password
                  </label>
                  <p className="text-blue-600 text-sm">Forgot Password?</p>
                </div>
                <input
                  type="password"
                  value={password}
                  onChange={e => setPassword(e.target.value)}
                  placeholder="Enter Your Password"
                  className="w-full px-4 py-2 rounded-full border border-gray-300 focus:ring-2 focus:ring-purple-300 outline-none"
                />
              </div>

              <Button
                radius="full"
                onPress={handleSignIn}
                className="w-full bg-gradient-to-r from-purple-500 to-blue-400 text-white py-2 rounded-full font-semibold hover:opacity-90 transition-all"
              >
                Sign In
              </Button>

              <div className="flex items-center gap-2 text-sm">
                <input
                  type="checkbox"
                  id="remember"
                  className="accent-purple-500 mt-1"
                />
                <label
                  htmlFor="remember"
                  className="text-[#1E1E76] text-base font-medium"
                >
                  Remember Me
                </label>
              </div>
            </div>

            {/* Right section */}
            <div className="w-full bg-gray-50 flex flex-col items-center justify-center gap-4 p-10 max-md:p-4 md:border-l max-md:hidden">
              <Button
                startContent={<FcGoogle className="text-xl" />}
                variant="bordered"
                className="rounded-full px-10 py-2 font-medium"
              >
                Sign In With Google
              </Button>
              <Button
                startContent={<FaApple className="text-xl" />}
                variant="bordered"
                className="rounded-full px-10 py-2 font-medium"
              >
                Sign In With Apple
              </Button>
            </div>
          </div>
        </ModalBody>
      </ModalContent>
    </Modal>
  );
}

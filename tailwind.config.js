import { heroui } from '@heroui/react';

/** @type {import('tailwindcss').Config} */
export default {
  content: [
    './app/**/*.{js,ts,jsx,tsx}',
    './components/**/*.{js,ts,jsx,tsx}',
    './src/**/*.{js,ts,jsx,tsx}',
    './node_modules/@heroui/theme/dist/**/*.{js,ts,jsx,tsx}',
  ],
  theme: {
    extend: {
      colors: {
        // Add custom colors that will override HeroUI defaults
        'primary-200': '#707FF5',
        'default-700': '#777777',

        primary: '#1E1E76',
        lightGray: '#8F9AA8',
        subtitle: '#1C1C1C',
        containerbg: '#F2F2FF',
      },
    },
  },
  plugins: [
    heroui({
      addCommonColors: false,
      themes: {
        light: {
          colors: {
            background: '#FFFFFF',
            foreground: '#11181C',
            primary: {
              foreground: '#FFFFFF',
              DEFAULT: '#1E1E76',
              50: '#F2F2FF',
              100: '#E6E6FF',
              200: '#707FF5',
              300: '#5A5AE6',
              400: '#4444D7',
              500: '#1E1E76',
              600: '#1A1A6B',
              700: '#161660',
              800: '#121255',
              900: '#0E0E4A',
            },
            secondary: {
              foreground: '#FFFFFF',
              DEFAULT: '#F2F2FF',
            },
            gray: {
              foreground: '#FFFFFF',
              DEFAULT: '#686C72',
            },
            default: {
              foreground: '#FFFFFF',
              DEFAULT: '#F5F5F5',
              Secondary: '#757575',
              100: '#FFFFFF',
              200: '#E8E8E8',
              300: '#D2D2D2',
              400: '#BBBBBB',
              500: '#A4A4A4',
              600: '#8E8E8E',
              700: '#777777',
              800: '#606060',
              900: '#4A4A4A',
              1000: '#333333',
            },
            red: {
              foreground: '#FFFFFF',
              DEFAULT: '#D00416',
              100: '#FB3748',
              200: '#D00416',
            },
            yellow: {
              foreground: '#FFFFFF',
              DEFAULT: '#E4AE0D',
              100: '#F7C83B',
              200: '#E4AE0D',
            },
            green: {
              foreground: '#FFFFFF',
              DEFAULT: '#1FC16B',
              100: '#84EBB4',
              200: '#1FC16B',
            },
            lightGray: {
              foreground: '#FFFFFF',
              DEFAULT: '#8F9AA8',
            },
            subtitle: {
              foreground: '#FFFFFF',
              DEFAULT: '#1C1C1C',
            },
          },
        },
      },
    }),
  ],
};

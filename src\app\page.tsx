'use client';

import LandingPage from '@/screen/landing';
import { useLandingData } from '@/hooks/useLandingData';

export default function Home() {
  const { filter, chat, recommendations } = useLandingData();

  return (
    <div>
      <LandingPage
        loadingComponents={{
          filter: filter.isLoading,
          chat: chat.isLoading,
          recommendations: recommendations.isLoading,
        }}
      />
    </div>
  );
}

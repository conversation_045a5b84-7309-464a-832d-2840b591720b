'use client';

import { useState, useEffect } from 'react';
import { useParams } from 'next/navigation';
import ExploreDetailsPage from '@/screen/ExploreDetails';

export default function ExploreDetails() {
  const [isLoading, setIsLoading] = useState(true);
  const params = useParams(); // { location: 'paris', title: 'city-guide' }

  useEffect(() => {
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 2000);

    return () => clearTimeout(timer);
  }, []);

  return (
    <div>
      <ExploreDetailsPage
        isLoading={isLoading}
        location={params.location as string}
        title={params.title as string}
      />
    </div>
  );
}

import { MapPointIcon, CalendarIcon } from '@/components/icons';
import { Card, CardBody, Button } from '@heroui/react';
import { useState } from 'react';
import Lightbox from 'yet-another-react-lightbox';
import 'yet-another-react-lightbox/styles.css';

import Image from 'next/image';
import Link from 'next/link';

interface Recommendation {
  title: string;
  duration: string;
  location: string;
  tags: string;
  media: {
    large: string;
    small: string[];
  };
  badge: string;
}

interface RecommendationCardProps {
  recommendations: Recommendation;
}
const RecommendationCard = ({ recommendations }: RecommendationCardProps) => {
  const [lightboxOpen, setLightboxOpen] = useState(false);
  const [lightboxIndex, setLightboxIndex] = useState(0);

  // Create images array for lightbox from recommendation media
  const images = [
    {
      src: recommendations.media.large,
      alt: 'Large Image',
    },
    ...recommendations.media.small.map((src, index) => ({
      src,
      alt: `Small Image ${index + 1}`,
    })),
  ];

  const handleViewAll = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setLightboxIndex(0);
    setLightboxOpen(true);
  };

  return (
    <div className="flex flex-col gap-2 flex-1 overflow-hidden mb-3">
      <Card
        key={`${recommendations.title}-${recommendations.location}-${recommendations.duration}`}
        className="bg-white border-none shadow-none hover:border-black hover:!bg-white transition-all duration-300 ease-in-out flex-shrink-0"
        isHoverable
        isPressable
        data-card="recommendation" // Used for DOM measurements
      >
        <CardBody>
          <div className="flex flex-row max-md:flex-col  justify-between rounded-xl cursor-pointer w-full">
            {/* Left section: Image and details */}
            <div className="flex flex-row max-md:flex-col gap-4">
              {/* grid Image  */}

              <div className="flex flex-col gap-1.5 md:w-[230px]">
                {/* Large Image */}
                <div className="w-full">
                  <Image
                    src={recommendations.media.large}
                    alt="Large Image"
                    width={230}
                    height={120}
                    className="w-full h-[120px] object-cover rounded-tl-xl rounded-tr-xl"
                  />
                </div>

                {/* Small Image Grid */}
                <div className="grid grid-cols-4 gap-1.5">
                  {recommendations.media.small.slice(0, 3).map((src, index) => (
                    <Image
                      key={index}
                      src={src}
                      alt={`Small Image ${index + 1}`}
                      width={400}
                      height={200}
                      className="w-full aspect-square object-cover"
                    />
                  ))}

                  {/* Blurred Last Image with Overlay Text */}
                  <div
                    className="relative cursor-pointer"
                    onClick={handleViewAll}
                    onKeyDown={e => {
                      if (e.key === 'Enter' || e.key === ' ') {
                        e.preventDefault();
                        e.stopPropagation();
                        setLightboxIndex(0);
                        setLightboxOpen(true);
                      }
                    }}
                    role="button"
                    tabIndex={0}
                    aria-label="View all images"
                  >
                    <Image
                      src={recommendations.media.small[3]}
                      alt="Small Image 4 (Blurred)"
                      width={400}
                      height={200}
                      className="w-full aspect-square object-cover rounded-br-md blur-xs"
                    />
                    <div className="absolute inset-0 text-sm flex items-center justify-center hover:bg-black/20 transition-colors">
                      <span className="text-white text-sm font-medium">
                        View All
                      </span>
                    </div>
                  </div>
                </div>
              </div>
              {/* Text content */}
              <div className="text-sm space-y-0.5">
                <p className="font-medium text-lg">{recommendations.title}</p>
                <div className="flex flex-row gap-3 py-2">
                  <div className="flex flex-row gap-1 items-center">
                    <CalendarIcon
                      size={14}
                      isAnimation={false}
                      className="text-black"
                    />
                    <p className=" text-md">{recommendations.duration}</p>
                  </div>

                  <div className="flex flex-row gap-1 items-center">
                    <MapPointIcon
                      size={14}
                      isAnimation={false}
                      className="text-black"
                    />
                    <p className=" text-md">{recommendations.location}</p>
                  </div>
                </div>
                <div>
                  <p className="text-default-700 text-md">
                    Book with only 30% of total payment
                  </p>
                  <p className="text-default-700 text-md">
                    Customize free of cost
                  </p>
                </div>

                <p className="text-md font-medium py-2">
                  Activities, Explore, Leisure, Family
                </p>

                <p className="text-md text-default-700">
                  Explore Berlin's historic neighborhoods and vibrant street
                  art.
                </p>
              </div>
            </div>
            {/* Right section: Action button */}
            <div className="flex flex-col gap-2 justify-between text-right">
              <p className="text-base font-bold">
                4.2{' '}
                <span className="text-default-700 font-medium">
                  (103 Ratings)
                </span>
              </p>
              <div className="text-right">
                <div className="">
                  <span className="text-default-700 line-through text-md">
                    $3,500
                  </span>
                  <span className="font-bold ml-3 text-xl">$2,999</span>
                </div>
                <div className="flex justify-end py-2">
                  <p className="text-default-700 text-md w-[130px] text-right">
                    $150 taxes & fees Per Person
                  </p>
                </div>
                <Link
                  href={`/explore/${recommendations.location
                    .toLowerCase()
                    .replace(/\s/g, '-')}/${recommendations.title
                    .toLowerCase()
                    .replace(/\s/g, '-')}`}
                  // className="px-5 py-2 rounded-full bg-primary text-white text-sm font-semibold hover:opacity-90 transition text-center"
                >
                  <Button
                    color="primary"
                    // variant="bordered"
                    size="sm"
                    className="font-semibold  rounded-full bg-primary-200 text-white"
                  >
                    Book Trip
                  </Button>
                </Link>
              </div>
            </div>
          </div>
        </CardBody>
      </Card>

      {/* Lightbox Component */}
      <Lightbox
        open={lightboxOpen}
        close={() => setLightboxOpen(false)}
        slides={images}
        index={lightboxIndex}
        on={{
          view: ({ index }) => setLightboxIndex(index),
        }}
      />
    </div>
  );
};

export default RecommendationCard;

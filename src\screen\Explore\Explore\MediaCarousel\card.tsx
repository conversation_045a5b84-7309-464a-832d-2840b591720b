'use client';

import { HeartIcon } from '@/components/icons';
import { Card, CardBody, Image } from '@heroui/react';

interface MediaCardProps {
  item: {
    img: string;
    heading: string;
  };
}

export default function MediaCardComp({ item }: MediaCardProps) {
  return (
    <Card
      isPressable
      shadow="sm"
      onPress={() => {}}
      className="shadow-none rounded-sm bg-transparent w-full"
    >
      <CardBody className="relative overflow-visible p-0">
        {/* Gradient Overlay */}

        {/* Image */}
        <div className=" relative">
          <Image
            alt={item.img}
            className="min-w-full object-cover h-[220px] shadow-none"
            radius="sm"
            src={item.img}
            width="100%"
          />
          <div className=" bg-gray absolute bottom-1 right-1 z-50 p-2 rounded-md">
            <HeartIcon size={18} className="text-white" />
          </div>
        </div>

        <div className="mt-1">
          <p className="text-base font-bold text-start text-[#25233A]">
            {item.heading}
          </p>
          <p className="text-[#868383] text-sm -mt-0.5">{item.heading}</p>
        </div>
      </CardBody>
    </Card>
  );
}
